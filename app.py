# app.py

import firebase_admin
from firebase_admin import credentials, firestore, storage
import google.generativeai as genai
import os
from dotenv import load_dotenv
import logging
import sys
from typing import Optional

# --- Setup Logging ---
# Using the logging module is better than print() for production applications.
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stdout,  # Direct logs to stdout, suitable for cloud environments
)

# --- Load Environment Variables ---
# For local development, it's useful to load variables from a .env file.
load_dotenv()

# --- Type Hinting for Global Clients ---
# It's good practice to define the types for global clients.
db: Optional[firestore.Client] = None
bucket: Optional[storage.Bucket] = None
gemini_model: Optional[genai.GenerativeModel] = None


def initialize_firebase() -> tuple[Optional[firestore.Client], Optional[storage.Bucket]]:
    """
    Initializes the Firebase Admin SDK.

    Uses a service account key for local development (path from env var)
    or Application Default Credentials when deployed in a Google Cloud environment.

    Returns:
        A tuple containing the Firestore client and Storage bucket, or (None, None) on failure.
    """
    try:
        storage_bucket_name = os.getenv("FIREBASE_STORAGE_BUCKET")
        if not storage_bucket_name:
            logging.error("FIREBASE_STORAGE_BUCKET environment variable is not set.")
            return None, None

        service_account_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY_PATH")
        if service_account_path and os.path.exists(service_account_path):
            logging.info(f"Initializing Firebase with service account: {service_account_path}")
            cred = credentials.Certificate(service_account_path)
        else:
            logging.info("Initializing Firebase with Application Default Credentials.")
            cred = credentials.ApplicationDefault()

        # Check if the app is already initialized to prevent errors on hot-reloads.
        if not firebase_admin._apps:
            firebase_admin.initialize_app(cred, {'storageBucket': storage_bucket_name})

        firestore_client = firestore.client()
        storage_bucket = storage.bucket()

        logging.info("Firebase Admin SDK initialized successfully.")
        return firestore_client, storage_bucket

    except Exception as e:
        logging.error(f"Error initializing Firebase Admin SDK: {e}", exc_info=True)
        return None, None


def initialize_gemini() -> Optional[genai.GenerativeModel]:
    """
    Initializes the Google Gemini API client.

    Uses an API key and a model name from environment variables.

    Returns:
        The Gemini model client, or None on failure.
    """
    try:
        api_key = os.getenv("GEMINI_API_KEY")